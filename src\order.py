import os
from enum import Enum

import dotenv

from bypass import place_order

dotenv.load_dotenv()

API_KEY = str(os.getenv("API_KEY"))
API_SECRET = str(os.getenv("API_SECRET"))
COOKIE = str(os.getenv("COOKIE"))

if API_KEY == "" or API_SECRET == "":
    raise ValueError("API_KEY or API_SECRET is not set in .env file")

if COOKIE == "":
    raise ValueError("COOKIE is not set in .env file")

if not COOKIE.startswith("WEB"):
    raise ValueError("COOKIE is not a valid WEB cookie, should start with 'WEB'")


class OrderSide(Enum):
    OPEN_LONG = 1
    CLOSE_SHORT = 2
    OPEN_SHORT = 3
    CLOSE_LONG = 4


# 取得毫秒級別的時間戳記（整數）
def create_order(side: OrderSide, quantity, testnet=True):
    obj = {
        "symbol": "BTC_USDT",
        "side": side.value,
        "openType": 1,
        "type": "1",
        "vol": quantity,
        "leverage": 1,
        "price": 2.5,
        "priceProtect": "0",
    }

    if testnet:
        url = "https://futures.testnet.mexc.com/api/v1/private/order/create"
    else:
        url = "https://futures.mexc.com/api/v1/private/order/create"

    response = place_order(COOKIE, obj, url)
    print(response)
