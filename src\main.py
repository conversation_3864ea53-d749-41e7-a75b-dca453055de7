import datetime
import time
from enum import Enum

import schedule

import fetcher
import insert_txt as txt
from order import OrderSide, create_order


class PositionStatus(Enum):
    NO_POSITION = "no_position"
    LONG = "long"
    SHORT = "short"


# 交易帳戶資訊
wins = 0
losses = 0
profit = 0
position_status = PositionStatus.NO_POSITION
size = 0
# 止盈止損價位
take_profit_price = 0
stop_loss_price = 0
testnet = True

# 設定止盈和止損點數 (這裡使用絕對點數，可以根據需要調整)
TP_POINTS = 200  # 止盈點數
SL_POINTS = 1200  # 止損點數


def job():
    global wins, losses, profit, position_status, take_profit_price, stop_loss_price, size

    current_timestamp = time.time()

    df = fetcher.get_last_candle()
    # print(type(df.iloc[-1]['close']))
    current_close = df.iloc[-1]["close"]

    # --- 1. 平倉邏輯 ---
    # 只有當有部位時才檢查平倉條件
    if position_status == PositionStatus.LONG:
        if current_close >= take_profit_price:
            txt.write("作多止盈平倉！")
            print("作多止盈平倉！")
            wins += 1
            profit += TP_POINTS
            position_status = PositionStatus.NO_POSITION
            create_order(OrderSide.CLOSE_LONG, size, testnet)
        elif current_close <= stop_loss_price:
            txt.write("作多止損平倉！")
            print("作多止損平倉！")
            losses += 1
            profit -= SL_POINTS
            position_status = PositionStatus.NO_POSITION
            create_order(OrderSide.CLOSE_LONG, size, testnet)
        print(f"loss:{losses}\nwins:{wins}")
    elif position_status == PositionStatus.SHORT:
        if current_close <= take_profit_price:
            txt.write("作空止盈平倉！")
            print("作空止盈平倉！")
            wins += 1
            profit += TP_POINTS
            position_status = PositionStatus.NO_POSITION
            create_order(OrderSide.CLOSE_SHORT, size, testnet)
        elif current_close >= stop_loss_price:
            txt.write("作空止損平倉！")
            print("作空止損平倉！")
            losses += 1
            profit -= SL_POINTS
            position_status = PositionStatus.NO_POSITION
            create_order(OrderSide.CLOSE_SHORT, size, testnet)
        print(f"loss:{losses}\nwins:{wins}")

    # --- 2. 開倉邏輯 ---
    # 只有當沒有部位時才檢查開倉條件
    if position_status == PositionStatus.NO_POSITION:
        long_condition = df.iloc[-1]["close"] < df.iloc[-1]["BBL"] and df.iloc[-2]["close"] < df.iloc[-2]["BBL"]
        short_condition = df.iloc[-1]["close"] > df.iloc[-1]["BBU"] and df.iloc[-2]["close"] > df.iloc[-2]["BBU"]

        if long_condition:
            size = round(40 / current_close, 5)
            print(size)
            create_order(OrderSide.OPEN_LONG, size, testnet)
            print("觸發作多條件，準備開倉！")

            take_profit_price = current_close + TP_POINTS
            stop_loss_price = current_close - SL_POINTS
            position_status = PositionStatus.LONG
            txt.write(f"{datetime.datetime.now()} : 止盈價位: {take_profit_price}，止損價位: {stop_loss_price}")
            print(f"止盈價位: {take_profit_price}，止損價位: {stop_loss_price}")
        elif short_condition:
            size = round(40 / current_close, 5)
            print(size)
            create_order(OrderSide.OPEN_SHORT, size, testnet)
            print("觸發作空條件，準備開倉！")

            take_profit_price = current_close - TP_POINTS
            stop_loss_price = current_close + SL_POINTS
            position_status = PositionStatus.SHORT
            txt.write(f"{datetime.datetime.now()} : 止盈價位: {take_profit_price}，止損價位: {stop_loss_price}")
            print(f"止盈價位: {take_profit_price}，止損價位: {stop_loss_price}")

    # print(f"目前部位狀態: {position_status}，總獲利: {profit}")
    cost_time = time.time() - current_timestamp
    print(
        f"{datetime.datetime.now()}  now :{int(df.iloc[-1]['close'])}"
        + f" BBU: {int(df.iloc[-1]['BBU'])} BBL:{int(df.iloc[-1]['BBL'])}"
    )
    print(f"資料取得耗時: {cost_time} 秒")


def main():
    schedule.every(30).seconds.do(job)

    job()
    while True:
        schedule.run_pending()
        time.sleep(2)


if __name__ == "__main__":
    main()
