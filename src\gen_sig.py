import hashlib
import hmac
import urllib.parse


def generate_signature(api_secret, params):
    """
    根據推測的 API 參數順序，為請求生成簽名。
    """
    # 這裡我們手動定義參數順序，這很可能是 API 要求的
    param_list = [
        ("symbol", params.get("symbol")),
        ("interval", params.get("interval")),
        ("timestamp", params.get("timestamp")),
        ("recvWindow", params.get("recvWindow")),
    ]

    # 確保值是字串，並處理空值
    final_params = [(k, str(v)) for k, v in param_list if v is not None]

    # 將參數串接成單一字串，不使用 sorted()
    query_string = urllib.parse.urlencode(final_params)

    # 生成 HMAC SHA256 雜湊值
    signature = hmac.new(api_secret.encode("utf-8"), query_string.encode("utf-8"), hashlib.sha256).hexdigest()

    # 返回小寫的簽名
    return signature.lower()
