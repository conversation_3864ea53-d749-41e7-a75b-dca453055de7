import os

import ccxt
import dotenv
import numpy as np
import pandas as pd

import gen_indicator

dotenv.load_dotenv()

API_KEY = str(os.getenv("API_KEY"))
API_SECRET = str(os.getenv("API_SECRET"))

if API_KEY == "" or API_SECRET == "":
    raise ValueError("API_KEY or API_SECRET is not set in .env file")


# 取得毫秒級別的時間戳記（整數）
def get_last_candle():
    mexc = ccxt.mexc(
        {
            "apiKey": API_KEY,
            "secret": API_SECRET,
        }
    )

    try:
        klines_data = mexc.fetch_ohlcv("BTC_USDT", "1m")
    except Exception as e:
        print(f"Error fetching data: {e}")
        raise e

    df = pd.DataFrame(klines_data)
    df.columns = ["time", "open", "high", "low", "close", "volume"]

    df = gen_indicator.bbd(df)

    df_converted = df.astype(np.float64)
    return df_converted
