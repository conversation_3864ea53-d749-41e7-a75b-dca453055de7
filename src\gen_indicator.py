def bbd(df):
    length = 20  # 週期的長度，通常為 20
    std_dev = 2  # 標準差的倍數，通常為 2

    # 3. 計算中軌 (Middle Band)
    # 使用 .rolling() 函式來計算 n 週期內的移動平均線
    df["BBM"] = df["close"].rolling(window=length).mean()

    # 4. 計算標準差 (Standard Deviation)
    # 使用 .rolling() 函式來計算 n 週期內的標準差
    df["std"] = df["close"].rolling(window=length).std()

    # 5. 計算上軌 (Upper Band) 和下軌 (Lower Band)
    df["BBU"] = df["BBM"] + (df["std"] * std_dev)
    df["BBL"] = df["BBM"] - (df["std"] * std_dev)
    return df
