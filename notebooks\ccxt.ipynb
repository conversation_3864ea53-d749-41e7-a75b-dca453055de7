{"cells": [{"cell_type": "code", "execution_count": 47, "id": "8c447587", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")"]}, {"cell_type": "code", "execution_count": 48, "id": "8c9ff63c", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["import dotenv\n", "dotenv.load_dotenv()"]}, {"cell_type": "code", "execution_count": 49, "id": "7fe2e36f", "metadata": {}, "outputs": [], "source": ["import os\n", "API_KEY = str(os.getenv(\"API_KEY\"))\n", "API_SECRET = str(os.getenv(\"API_SECRET\"))"]}, {"cell_type": "code", "execution_count": 50, "id": "76c8725d", "metadata": {}, "outputs": [], "source": ["import ccxt"]}, {"cell_type": "code", "execution_count": 51, "id": "61c7d10e", "metadata": {}, "outputs": [], "source": ["mexc = ccxt.mexc(\n", "    {\n", "        \"apiKey\": API_KEY,\n", "        \"secret\": API_SECRET,\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 52, "id": "defa5bfc", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "97dabe01", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(mexc.fetchOHLCV(\"BTC_USDT\", \"1m\")) # type: ignore\n"]}, {"cell_type": "code", "execution_count": 54, "id": "ff24ca81", "metadata": {}, "outputs": [], "source": ["df.columns = [\"時間\", \"開盤\", \"高\", \"低\", \"close\", \"volume\"]"]}, {"cell_type": "code", "execution_count": 55, "id": "1a86c362", "metadata": {}, "outputs": [], "source": ["df[\"時間\"] = pd.to_datetime(df[\"時間\"], unit=\"ms\")"]}, {"cell_type": "code", "execution_count": 56, "id": "99b6056a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "時間", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "開盤", "rawType": "float64", "type": "float"}, {"name": "高", "rawType": "float64", "type": "float"}, {"name": "低", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}], "ref": "1fe16a81-4ae9-4b8f-889d-d221abae339e", "rows": [["0", "2025-07-17 06:01:00", "118504.3", "118554.6", "118504.2", "118554.6", "353011.0"], ["1", "2025-07-17 06:02:00", "118554.6", "118573.5", "118523.9", "118573.4", "422162.0"], ["2", "2025-07-17 06:03:00", "118573.4", "118664.9", "118573.4", "118651.9", "834376.0"], ["3", "2025-07-17 06:04:00", "118651.9", "118667.2", "118651.2", "118666.7", "363768.0"], ["4", "2025-07-17 06:05:00", "118666.7", "118667.2", "118603.5", "118629.9", "199384.0"], ["5", "2025-07-17 06:06:00", "118629.9", "118687.5", "118629.9", "118687.5", "557706.0"], ["6", "2025-07-17 06:07:00", "118687.5", "118736.8", "118680.0", "118680.0", "666455.0"], ["7", "2025-07-17 06:08:00", "118680.0", "118730.8", "118680.0", "118709.3", "317082.0"], ["8", "2025-07-17 06:09:00", "118709.3", "118709.4", "118662.1", "118663.0", "226209.0"], ["9", "2025-07-17 06:10:00", "118663.0", "118676.5", "118600.1", "118600.1", "237500.0"], ["10", "2025-07-17 06:11:00", "118600.1", "118600.2", "118569.3", "118578.7", "329486.0"], ["11", "2025-07-17 06:12:00", "118578.7", "118578.8", "118508.5", "118508.6", "427440.0"], ["12", "2025-07-17 06:13:00", "118508.6", "118517.3", "118438.4", "118438.5", "452438.0"], ["13", "2025-07-17 06:14:00", "118438.5", "118471.2", "118431.6", "118471.1", "296707.0"], ["14", "2025-07-17 06:15:00", "118471.1", "118486.7", "118436.6", "118436.7", "140292.0"], ["15", "2025-07-17 06:16:00", "118436.7", "118436.7", "118379.1", "118431.1", "274645.0"], ["16", "2025-07-17 06:17:00", "118431.1", "118431.1", "118381.4", "118420.3", "476417.0"], ["17", "2025-07-17 06:18:00", "118420.3", "118420.3", "118331.5", "118349.9", "473202.0"], ["18", "2025-07-17 06:19:00", "118349.9", "118367.0", "118307.6", "118367.0", "582578.0"], ["19", "2025-07-17 06:20:00", "118367.0", "118416.8", "118350.0", "118372.7", "1034516.0"], ["20", "2025-07-17 06:21:00", "118372.7", "118518.4", "118372.7", "118518.4", "598220.0"], ["21", "2025-07-17 06:22:00", "118518.4", "118551.4", "118466.5", "118489.0", "405096.0"], ["22", "2025-07-17 06:23:00", "118489.0", "118565.2", "118489.0", "118547.6", "526821.0"], ["23", "2025-07-17 06:24:00", "118547.6", "118747.2", "118547.6", "118730.0", "783926.0"], ["24", "2025-07-17 06:25:00", "118730.0", "118752.5", "118666.0", "118700.0", "620081.0"], ["25", "2025-07-17 06:26:00", "118700.0", "118744.5", "118676.9", "118744.5", "304310.0"], ["26", "2025-07-17 06:27:00", "118744.5", "118744.5", "118700.0", "118729.0", "321735.0"], ["27", "2025-07-17 06:28:00", "118729.0", "118741.6", "118728.9", "118730.1", "248334.0"], ["28", "2025-07-17 06:29:00", "118730.1", "118750.0", "118620.3", "118630.1", "421692.0"], ["29", "2025-07-17 06:30:00", "118630.1", "118652.4", "118558.1", "118576.9", "613052.0"], ["30", "2025-07-17 06:31:00", "118576.9", "118623.1", "118560.0", "118612.5", "204489.0"], ["31", "2025-07-17 06:32:00", "118612.5", "118618.2", "118500.0", "118500.1", "400593.0"], ["32", "2025-07-17 06:33:00", "118500.1", "118627.1", "118500.0", "118609.0", "214376.0"], ["33", "2025-07-17 06:34:00", "118609.0", "118609.0", "118520.5", "118520.6", "229494.0"], ["34", "2025-07-17 06:35:00", "118520.6", "118520.6", "118433.4", "118433.5", "461349.0"], ["35", "2025-07-17 06:36:00", "118433.5", "118433.6", "118378.4", "118378.5", "280701.0"], ["36", "2025-07-17 06:37:00", "118378.5", "118427.4", "118357.2", "118418.2", "777072.0"], ["37", "2025-07-17 06:38:00", "118418.2", "118439.1", "118396.4", "118396.5", "308396.0"], ["38", "2025-07-17 06:39:00", "118396.5", "118412.9", "118363.7", "118409.6", "475246.0"], ["39", "2025-07-17 06:40:00", "118409.6", "118409.6", "118350.0", "118355.0", "344302.0"], ["40", "2025-07-17 06:41:00", "118355.0", "118355.1", "118337.0", "118339.8", "519569.0"], ["41", "2025-07-17 06:42:00", "118339.8", "118430.5", "118339.8", "118423.6", "817261.0"], ["42", "2025-07-17 06:43:00", "118423.6", "118435.2", "118400.0", "118400.1", "483254.0"], ["43", "2025-07-17 06:44:00", "118400.1", "118421.3", "118396.3", "118407.8", "173170.0"], ["44", "2025-07-17 06:45:00", "118407.8", "118442.9", "118407.7", "118408.0", "138463.0"], ["45", "2025-07-17 06:46:00", "118408.0", "118408.0", "118323.2", "118362.7", "264830.0"], ["46", "2025-07-17 06:47:00", "118362.7", "118417.3", "118362.7", "118417.3", "203542.0"], ["47", "2025-07-17 06:48:00", "118417.3", "118420.5", "118400.0", "118420.5", "103948.0"], ["48", "2025-07-17 06:49:00", "118420.5", "118444.4", "118409.7", "118428.6", "124795.0"], ["49", "2025-07-17 06:50:00", "118428.6", "118428.6", "118371.6", "118371.6", "476350.0"]], "shape": {"columns": 6, "rows": 2000}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>時間</th>\n", "      <th>開盤</th>\n", "      <th>高</th>\n", "      <th>低</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-07-17 06:01:00</td>\n", "      <td>118504.3</td>\n", "      <td>118554.6</td>\n", "      <td>118504.2</td>\n", "      <td>118554.6</td>\n", "      <td>353011.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-07-17 06:02:00</td>\n", "      <td>118554.6</td>\n", "      <td>118573.5</td>\n", "      <td>118523.9</td>\n", "      <td>118573.4</td>\n", "      <td>422162.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-07-17 06:03:00</td>\n", "      <td>118573.4</td>\n", "      <td>118664.9</td>\n", "      <td>118573.4</td>\n", "      <td>118651.9</td>\n", "      <td>834376.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-07-17 06:04:00</td>\n", "      <td>118651.9</td>\n", "      <td>118667.2</td>\n", "      <td>118651.2</td>\n", "      <td>118666.7</td>\n", "      <td>363768.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-07-17 06:05:00</td>\n", "      <td>118666.7</td>\n", "      <td>118667.2</td>\n", "      <td>118603.5</td>\n", "      <td>118629.9</td>\n", "      <td>199384.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1995</th>\n", "      <td>2025-07-18 15:16:00</td>\n", "      <td>117870.1</td>\n", "      <td>117921.3</td>\n", "      <td>117769.1</td>\n", "      <td>117775.1</td>\n", "      <td>1004049.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1996</th>\n", "      <td>2025-07-18 15:17:00</td>\n", "      <td>117775.1</td>\n", "      <td>117834.9</td>\n", "      <td>117753.7</td>\n", "      <td>117781.4</td>\n", "      <td>723815.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1997</th>\n", "      <td>2025-07-18 15:18:00</td>\n", "      <td>117781.4</td>\n", "      <td>117797.2</td>\n", "      <td>117753.7</td>\n", "      <td>117753.7</td>\n", "      <td>673947.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1998</th>\n", "      <td>2025-07-18 15:19:00</td>\n", "      <td>117753.7</td>\n", "      <td>117753.7</td>\n", "      <td>117551.0</td>\n", "      <td>117565.1</td>\n", "      <td>2001409.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999</th>\n", "      <td>2025-07-18 15:20:00</td>\n", "      <td>117565.1</td>\n", "      <td>117610.5</td>\n", "      <td>117564.9</td>\n", "      <td>117608.5</td>\n", "      <td>61123.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2000 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                      時間        開盤         高         低     close     volume\n", "0    2025-07-17 06:01:00  118504.3  118554.6  118504.2  118554.6   353011.0\n", "1    2025-07-17 06:02:00  118554.6  118573.5  118523.9  118573.4   422162.0\n", "2    2025-07-17 06:03:00  118573.4  118664.9  118573.4  118651.9   834376.0\n", "3    2025-07-17 06:04:00  118651.9  118667.2  118651.2  118666.7   363768.0\n", "4    2025-07-17 06:05:00  118666.7  118667.2  118603.5  118629.9   199384.0\n", "...                  ...       ...       ...       ...       ...        ...\n", "1995 2025-07-18 15:16:00  117870.1  117921.3  117769.1  117775.1  1004049.0\n", "1996 2025-07-18 15:17:00  117775.1  117834.9  117753.7  117781.4   723815.0\n", "1997 2025-07-18 15:18:00  117781.4  117797.2  117753.7  117753.7   673947.0\n", "1998 2025-07-18 15:19:00  117753.7  117753.7  117551.0  117565.1  2001409.0\n", "1999 2025-07-18 15:20:00  117565.1  117610.5  117564.9  117608.5    61123.0\n", "\n", "[2000 rows x 6 columns]"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "02df9ddd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "arbitrage-trading", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}