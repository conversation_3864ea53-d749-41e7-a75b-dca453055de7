"""
Configuration settings using Pydantic for validation
"""

import os
from typing import Optional
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

from .constants import (
    DEFAULT_TP_POINTS,
    DEFAULT_SL_POINTS,
    DEFAULT_POSITION_SIZE,
    DEFAULT_BB_PERIOD,
    DEFAULT_BB_STD_DEV,
    DEFAULT_SCHEDULE_INTERVAL,
    DEFAULT_SYMBOL,
    DEFAULT_TIMEFRAME,
    TradingMode,
)

# 載入環境變數
load_dotenv()


class APIConfig(BaseModel):
    """API 配置"""
    api_key: str = Field(..., description="MEXC API Key")
    api_secret: str = Field(..., description="MEXC API Secret")
    cookie: str = Field(..., description="MEXC Web Cookie")
    
    @validator('api_key', 'api_secret')
    def validate_not_empty(cls, v):
        if not v or v.strip() == "":
            raise ValueError("API key and secret cannot be empty")
        return v
    
    @validator('cookie')
    def validate_cookie(cls, v):
        if not v or not v.startswith('WEB'):
            raise ValueError("Cookie must start with 'WEB'")
        return v


class TradingConfig(BaseModel):
    """交易配置"""
    symbol: str = Field(default=DEFAULT_SYMBOL, description="交易對")
    timeframe: str = Field(default=DEFAULT_TIMEFRAME, description="時間框架")
    position_size_usdt: float = Field(default=DEFAULT_POSITION_SIZE, description="部位大小（USDT）")
    take_profit_points: float = Field(default=DEFAULT_TP_POINTS, description="止盈點數")
    stop_loss_points: float = Field(default=DEFAULT_SL_POINTS, description="止損點數")
    bb_period: int = Field(default=DEFAULT_BB_PERIOD, description="布林帶週期")
    bb_std_dev: float = Field(default=DEFAULT_BB_STD_DEV, description="布林帶標準差倍數")
    schedule_interval: int = Field(default=DEFAULT_SCHEDULE_INTERVAL, description="執行間隔（秒）")
    trading_mode: TradingMode = Field(default=TradingMode.TESTNET, description="交易模式")
    
    @validator('position_size_usdt', 'take_profit_points', 'stop_loss_points')
    def validate_positive(cls, v):
        if v <= 0:
            raise ValueError("Value must be positive")
        return v
    
    @validator('bb_period', 'schedule_interval')
    def validate_positive_int(cls, v):
        if v <= 0:
            raise ValueError("Value must be positive integer")
        return v


class LoggingConfig(BaseModel):
    """日誌配置"""
    level: str = Field(default="INFO", description="日誌級別")
    log_file: str = Field(default="logs/trading.log", description="日誌文件路徑")
    max_file_size: str = Field(default="10 MB", description="最大文件大小")
    backup_count: int = Field(default=5, description="備份文件數量")
    
    @validator('level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class Settings(BaseModel):
    """主要設定類別"""
    api: APIConfig
    trading: TradingConfig = Field(default_factory=TradingConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    @classmethod
    def from_env(cls) -> "Settings":
        """從環境變數創建設定"""
        api_key = os.getenv("API_KEY", "")
        api_secret = os.getenv("API_SECRET", "")
        cookie = os.getenv("COOKIE", "")
        
        if not all([api_key, api_secret, cookie]):
            raise ValueError("Missing required environment variables: API_KEY, API_SECRET, COOKIE")
        
        api_config = APIConfig(
            api_key=api_key,
            api_secret=api_secret,
            cookie=cookie
        )
        
        return cls(api=api_config)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
